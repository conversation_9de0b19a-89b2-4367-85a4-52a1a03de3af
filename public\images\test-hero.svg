<svg width="500" height="400" viewBox="0 0 500 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="500" height="400" fill="url(#gradient1)"/>
  
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.1" />
      <stop offset="50%" style="stop-color:#6366F1;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:0.1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3B82F6" />
      <stop offset="100%" style="stop-color:#6366F1" />
    </linearGradient>
  </defs>
  
  <!-- Main dashboard container -->
  <rect x="50" y="50" width="400" height="300" rx="20" fill="white" stroke="#E5E7EB" stroke-width="2"/>
  
  <!-- Header bar -->
  <rect x="50" y="50" width="400" height="60" rx="20" fill="url(#gradient2)"/>
  <rect x="50" y="90" width="400" height="20" fill="url(#gradient2)"/>
  
  <!-- Title -->
  <text x="250" y="85" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">SGDI Analytics Dashboard</text>
  
  <!-- Chart 1 - Bar Chart -->
  <g transform="translate(80, 140)">
    <rect x="0" y="40" width="15" height="60" fill="#3B82F6" rx="2"/>
    <rect x="20" y="30" width="15" height="70" fill="#6366F1" rx="2"/>
    <rect x="40" y="20" width="15" height="80" fill="#8B5CF6" rx="2"/>
    <rect x="60" y="35" width="15" height="65" fill="#3B82F6" rx="2"/>
    <rect x="80" y="15" width="15" height="85" fill="#6366F1" rx="2"/>
    <text x="50" y="120" text-anchor="middle" fill="#6B7280" font-family="Arial" font-size="12">Demandes RH</text>
  </g>
  
  <!-- Chart 2 - Pie Chart -->
  <g transform="translate(250, 160)">
    <circle cx="40" cy="40" r="35" fill="none" stroke="#E5E7EB" stroke-width="8"/>
    <circle cx="40" cy="40" r="35" fill="none" stroke="#3B82F6" stroke-width="8" 
            stroke-dasharray="110 220" stroke-dashoffset="0" transform="rotate(-90 40 40)"/>
    <circle cx="40" cy="40" r="35" fill="none" stroke="#6366F1" stroke-width="8" 
            stroke-dasharray="55 275" stroke-dashoffset="-110" transform="rotate(-90 40 40)"/>
    <text x="40" y="100" text-anchor="middle" fill="#6B7280" font-family="Arial" font-size="12">Répartition</text>
  </g>
  
  <!-- Chart 3 - Line Chart -->
  <g transform="translate(350, 140)">
    <polyline points="0,80 15,60 30,40 45,50 60,30 75,35" 
              fill="none" stroke="#10B981" stroke-width="3" stroke-linecap="round"/>
    <circle cx="0" cy="80" r="3" fill="#10B981"/>
    <circle cx="15" cy="60" r="3" fill="#10B981"/>
    <circle cx="30" cy="40" r="3" fill="#10B981"/>
    <circle cx="45" cy="50" r="3" fill="#10B981"/>
    <circle cx="60" cy="30" r="3" fill="#10B981"/>
    <circle cx="75" cy="35" r="3" fill="#10B981"/>
    <text x="37" y="120" text-anchor="middle" fill="#6B7280" font-family="Arial" font-size="12">Performance</text>
  </g>
  
  <!-- Stats boxes -->
  <g transform="translate(80, 260)">
    <rect x="0" y="0" width="80" height="50" rx="8" fill="#F3F4F6"/>
    <text x="40" y="20" text-anchor="middle" fill="#3B82F6" font-family="Arial" font-size="16" font-weight="bold">1,247</text>
    <text x="40" y="35" text-anchor="middle" fill="#6B7280" font-family="Arial" font-size="10">Demandes</text>
  </g>
  
  <g transform="translate(180, 260)">
    <rect x="0" y="0" width="80" height="50" rx="8" fill="#F3F4F6"/>
    <text x="40" y="20" text-anchor="middle" fill="#6366F1" font-family="Arial" font-size="16" font-weight="bold">98%</text>
    <text x="40" y="35" text-anchor="middle" fill="#6B7280" font-family="Arial" font-size="10">Satisfaction</text>
  </g>
  
  <g transform="translate(280, 260)">
    <rect x="0" y="0" width="80" height="50" rx="8" fill="#F3F4F6"/>
    <text x="40" y="20" text-anchor="middle" fill="#8B5CF6" font-family="Arial" font-size="16" font-weight="bold">2.5h</text>
    <text x="40" y="35" text-anchor="middle" fill="#6B7280" font-family="Arial" font-size="10">Temps moyen</text>
  </g>
  
  <!-- Floating elements -->
  <circle cx="100" cy="100" r="3" fill="#3B82F6" opacity="0.6"/>
  <circle cx="400" cy="120" r="2" fill="#6366F1" opacity="0.6"/>
  <circle cx="150" cy="350" r="2.5" fill="#8B5CF6" opacity="0.6"/>
  <circle cx="350" cy="350" r="2" fill="#10B981" opacity="0.6"/>
</svg>
