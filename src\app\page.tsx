'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  FileText, Calendar, Shield, Users, CheckCircle, Clock, ArrowRight,
  Sparkles, Star, Play, ChevronDown, Award, TrendingUp, Zap,
  MessageSquare, BarChart3, Globe, Smartphone, Lock,
  CheckCircle2, ArrowUpRight, Menu, X
} from "lucide-react";

export default function HomePage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeTestimonial, setActiveTestimonial] = useState(0);

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Directrice RH, TechCorp",
      content: "SGDI a transformé notre gestion des demandes. Nous avons réduit le temps de traitement de 60% et nos employés sont plus satisfaits.",
      rating: 5,
      avatar: "SD"
    },
    {
      name: "<PERSON>",
      role: "Responsable RH, InnovatePlus",
      content: "L'interface est intuitive et le suivi en temps réel nous permet d'être plus réactifs. Un outil pratique pour notre équipe.",
      rating: 5,
      avatar: "ML"
    },
    {
      name: "<PERSON>",
      role: "Gestionnaire RH, StartupTech",
      content: "Une solution qui simplifie vraiment les démarches administratives. Nos employés apprécient la facilité d'utilisation.",
      rating: 5,
      avatar: "JM"
    }
  ];

  const stats = [
    { number: "2K+", label: "Employés actifs", description: "Utilisateurs quotidiens" },
    { number: "50+", label: "Entreprises", description: "Nous font confiance" },
    { number: "95%", label: "Satisfaction", description: "Taux de satisfaction" },
    { number: "24h", label: "Temps de traitement", description: "Délai moyen" }
  ];

  const benefits = [
    {
      icon: Clock,
      title: "Gain de temps",
      description: "Réduisez de 70% le temps consacré aux tâches administratives",
      metric: "70%"
    },
    {
      icon: Users,
      title: "Satisfaction employés",
      description: "Améliorez l'expérience de vos collaborateurs",
      metric: "95%"
    },
    {
      icon: TrendingUp,
      title: "Productivité",
      description: "Augmentez l'efficacité de votre équipe RH",
      metric: "+40%"
    }
  ];

  const features = [
    {
      icon: FileText,
      title: "Demandes d'attestations",
      description: "Créez et suivez vos demandes d'attestations de travail, de salaire et autres documents officiels en quelques clics.",
      details: ["Génération automatique", "Modèles personnalisables", "Suivi en temps réel"]
    },
    {
      icon: Calendar,
      title: "Gestion des congés",
      description: "Planifiez et gérez tous types de congés avec un calendrier intelligent et des processus automatisés.",
      details: ["Congés maternité/paternité", "Congés payés", "Arrêts maladie"]
    },
    {
      icon: Shield,
      title: "Sécurité avancée",
      description: "Protection des données avec chiffrement et conformité RGPD garantie pour vos informations sensibles.",
      details: ["Chiffrement sécurisé", "Authentification forte", "Audit complet"]
    },
    {
      icon: BarChart3,
      title: "Tableaux de bord",
      description: "Visualisez vos données RH avec des tableaux de bord personnalisés pour optimiser vos processus.",
      details: ["Rapports automatisés", "Métriques en temps réel", "Export de données"]
    },
    {
      icon: Smartphone,
      title: "Interface moderne",
      description: "Accédez à toutes les fonctionnalités depuis n'importe quel appareil avec notre interface responsive.",
      details: ["Design adaptatif", "Navigation intuitive", "Accès mobile"]
    },
    {
      icon: Globe,
      title: "Support complet",
      description: "Bénéficiez d'un support technique et d'une documentation complète pour utiliser la plateforme.",
      details: ["Documentation détaillée", "Support technique", "Formation utilisateurs"]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 relative overflow-hidden">
      {/* Fond animé global */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-indigo-500/5"></div>
        <div className="absolute top-0 left-0 w-full h-full opacity-30">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-400/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-40 right-10 w-96 h-96 bg-indigo-400/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
          <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-purple-400/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '4s'}}></div>
        </div>
      </div>

      {/* Header avec navigation */}
      <header className="fixed top-0 w-full z-50 bg-white/90 backdrop-blur-xl border-b border-slate-200/50 shadow-xl shadow-slate-200/20">
        <div className="container mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4 group">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="w-11 h-11 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-xl shadow-blue-500/30">
                    <FileText className="w-6 h-6 text-white" />
                  </div>
                  <div className="absolute -inset-1 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl blur opacity-20 group-hover:opacity-40 transition-opacity duration-500"></div>
                  <div className="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-slate-800 via-blue-800 to-indigo-800 bg-clip-text text-transparent group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-500">
                  SGDI
                </span>
              </div>
              <div className="hidden sm:block h-6 w-px bg-gradient-to-b from-transparent via-slate-300 to-transparent"></div>
              <span className="hidden sm:inline text-sm text-slate-600 font-semibold group-hover:text-blue-600 transition-colors duration-300">
                Système de Gestion des Demandes
              </span>
            </div>

            {/* Navigation desktop */}
            <nav className="hidden md:flex items-center space-x-8">
              <a href="#features" className="relative text-slate-600 hover:text-blue-600 font-semibold transition-all duration-300 group px-3 py-2 rounded-lg hover:bg-blue-50/50">
                <span className="relative z-10">Fonctionnalités</span>
                <span className="absolute -bottom-1 left-3 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-indigo-600 group-hover:w-[calc(100%-1.5rem)] transition-all duration-300 rounded-full"></span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-50/0 to-indigo-50/0 group-hover:from-blue-50/50 group-hover:to-indigo-50/50 rounded-lg transition-all duration-300"></div>
              </a>
              <a href="#testimonials" className="relative text-slate-600 hover:text-blue-600 font-semibold transition-all duration-300 group px-3 py-2 rounded-lg hover:bg-blue-50/50">
                <span className="relative z-10">Témoignages</span>
                <span className="absolute -bottom-1 left-3 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-indigo-600 group-hover:w-[calc(100%-1.5rem)] transition-all duration-300 rounded-full"></span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-50/0 to-indigo-50/0 group-hover:from-blue-50/50 group-hover:to-indigo-50/50 rounded-lg transition-all duration-300"></div>
              </a>
              <a href="#contact" className="relative text-slate-600 hover:text-blue-600 font-semibold transition-all duration-300 group px-3 py-2 rounded-lg hover:bg-blue-50/50">
                <span className="relative z-10">Contact</span>
                <span className="absolute -bottom-1 left-3 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-indigo-600 group-hover:w-[calc(100%-1.5rem)] transition-all duration-300 rounded-full"></span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-50/0 to-indigo-50/0 group-hover:from-blue-50/50 group-hover:to-indigo-50/50 rounded-lg transition-all duration-300"></div>
              </a>
            </nav>

            <div className="flex items-center space-x-4">
              <Link href="/auth/signin" className="hidden md:inline-flex text-slate-600 hover:text-blue-600 font-semibold transition-all duration-300 relative group px-3 py-2 rounded-lg hover:bg-blue-50/50">
                <span className="relative z-10">Se connecter</span>
                <span className="absolute -bottom-1 left-3 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-indigo-600 group-hover:w-[calc(100%-1.5rem)] transition-all duration-300 rounded-full"></span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-50/0 to-indigo-50/0 group-hover:from-blue-50/50 group-hover:to-indigo-50/50 rounded-lg transition-all duration-300"></div>
              </Link>
              <Link href="/auth/signin" className="relative bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white px-6 py-3 rounded-xl hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 transition-all duration-500 transform hover:scale-105 hover:shadow-2xl shadow-xl shadow-blue-500/30 font-semibold group overflow-hidden">
                <span className="relative z-10 flex items-center">
                  Commencer
                  <ArrowUpRight className="w-4 h-4 ml-2 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-xl"></div>
              </Link>

              {/* Menu mobile */}
              <button
                className="md:hidden p-2.5 rounded-xl hover:bg-blue-50 transition-all duration-300 group"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ?
                  <X className="w-5 h-5 text-slate-600 group-hover:text-blue-600 transition-colors duration-300" /> :
                  <Menu className="w-5 h-5 text-slate-600 group-hover:text-blue-600 transition-colors duration-300" />
                }
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Menu mobile */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-40 bg-white/95 backdrop-blur-xl pt-20 md:hidden animate-in slide-in-from-top duration-300">
          <nav className="flex flex-col space-y-8 p-8">
            <a href="#features" className="text-xl text-slate-700 font-semibold hover:text-blue-600 transition-all duration-300 transform hover:translate-x-2 flex items-center group">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-4 group-hover:w-4 transition-all duration-300"></span>
              Fonctionnalités
            </a>
            <a href="#testimonials" className="text-xl text-slate-700 font-semibold hover:text-blue-600 transition-all duration-300 transform hover:translate-x-2 flex items-center group">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-4 group-hover:w-4 transition-all duration-300"></span>
              Témoignages
            </a>
            <a href="#contact" className="text-xl text-slate-700 font-semibold hover:text-blue-600 transition-all duration-300 transform hover:translate-x-2 flex items-center group">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-4 group-hover:w-4 transition-all duration-300"></span>
              Contact
            </a>
            <div className="h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent"></div>
            <Link href="/auth/signin" className="text-xl text-slate-700 font-semibold hover:text-blue-600 transition-all duration-300 transform hover:translate-x-2 flex items-center group">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-4 group-hover:w-4 transition-all duration-300"></span>
              Se connecter
            </Link>
          </nav>
        </div>
      )}

      {/* Section Hero */}
      <section className="pt-28 pb-20 relative overflow-hidden">
        {/* Fond complexe avec plusieurs couches */}
        <div className="absolute inset-0 bg-gradient-to-br from-white via-blue-50/40 to-indigo-50/30"></div>
        <div className="absolute inset-0 opacity-60">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, rgb(59 130 246 / 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgb(99 102 241 / 0.1) 0%, transparent 50%)`,
          }}></div>
        </div>
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgb(148 163 184 / 0.2) 1px, transparent 0)`,
            backgroundSize: '24px 24px'
          }}></div>
        </div>

        {/* Éléments flottants animés */}
        <div className="absolute top-20 left-10 w-16 h-16 bg-blue-400/10 rounded-full blur-xl animate-bounce" style={{animationDuration: '3s'}}></div>
        <div className="absolute top-32 right-20 w-12 h-12 bg-indigo-400/10 rounded-full blur-xl animate-bounce" style={{animationDuration: '4s', animationDelay: '1s'}}></div>
        <div className="absolute bottom-16 left-1/4 w-20 h-20 bg-purple-400/10 rounded-full blur-xl animate-bounce" style={{animationDuration: '5s', animationDelay: '2s'}}></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center max-w-5xl mx-auto">
            <div className="inline-flex items-center space-x-3 bg-gradient-to-r from-blue-50/80 via-indigo-50/80 to-purple-50/80 backdrop-blur-sm border border-blue-200/50 rounded-full px-6 py-3 mb-6 shadow-lg shadow-blue-500/10 group hover:shadow-xl hover:shadow-blue-500/20 transition-all duration-500">
              <div className="relative">
                <div className="w-2.5 h-2.5 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="absolute inset-0 w-2.5 h-2.5 bg-blue-400 rounded-full animate-ping"></div>
              </div>
              <span className="text-xs font-bold text-slate-700 group-hover:text-blue-700 transition-colors duration-300">✨ Nouveau : IA intégrée pour l'automatisation</span>
            </div>

            <h1 className="text-4xl md:text-5xl font-black text-slate-900 mb-6 leading-tight">
              <span className="inline-block animate-in slide-in-from-left duration-700">Simplifiez</span>{' '}
              <span className="inline-block animate-in slide-in-from-right duration-700 delay-200">votre</span>{' '}
              <span className="inline-block animate-in slide-in-from-left duration-700 delay-400">gestion</span>
              <span className="block bg-gradient-to-r from-blue-600 via-indigo-600 via-purple-600 to-blue-800 bg-clip-text text-transparent animate-in slide-in-from-bottom duration-700 delay-600 bg-[length:200%_100%] animate-pulse">
                des demandes RH
              </span>
            </h1>

            <p className="text-lg md:text-xl text-slate-600 mb-8 max-w-3xl mx-auto leading-relaxed font-medium animate-in fade-in duration-700 delay-800">
              SGDI vous aide à gérer efficacement les demandes d'attestations, congés et autres
              démarches administratives avec une interface moderne et intuitive.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-in slide-in-from-bottom duration-700 delay-1000">
              <Link href="/auth/signin" className="group relative bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-bold hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 transition-all duration-500 transform hover:scale-105 shadow-xl shadow-blue-500/25 hover:shadow-blue-500/40 inline-flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="flex items-center relative z-10">
                  Commencer maintenant
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-2 transition-transform duration-300" />
                </div>
              </Link>
              <button className="group flex items-center justify-center border-2 border-slate-300/60 text-slate-700 px-8 py-4 rounded-xl text-lg font-bold hover:border-blue-400 hover:bg-blue-50/50 transition-all duration-500 bg-white/70 backdrop-blur-sm shadow-lg hover:shadow-xl transform hover:scale-105">
                <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
                Voir la démo
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-5xl mx-auto animate-in slide-in-from-bottom duration-700 delay-1200">
              {stats.map((stat, index) => (
                <div key={index} className="group relative text-center p-6 bg-white/80 backdrop-blur-lg rounded-2xl border border-slate-200/40 shadow-lg hover:shadow-xl transition-all duration-700 hover:-translate-y-2 hover:bg-white/95 overflow-hidden">
                  {/* Effet de brillance au hover */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                  <div className="relative z-10">
                    <div className="text-2xl md:text-3xl font-black text-slate-900 mb-2 group-hover:scale-110 transition-all duration-500 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                      {stat.number}
                    </div>
                    <div className="text-slate-800 text-sm font-bold mb-1 group-hover:text-blue-600 transition-colors duration-300">
                      {stat.label}
                    </div>
                    <div className="text-slate-500 text-xs font-semibold">
                      {stat.description}
                    </div>
                  </div>

                  {/* Bordure animée */}
                  <div className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-blue-400/50 transition-all duration-500"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Section Avantages */}
      <section className="py-16 relative overflow-hidden">
        {/* Fond avec dégradé complexe */}
        <div className="absolute inset-0 bg-gradient-to-br from-white via-blue-50/20 to-indigo-50/30"></div>
        <div className="absolute inset-0 opacity-40">
          <div className="absolute inset-0" style={{
            backgroundImage: `conic-gradient(from 0deg at 50% 50%, transparent 0deg, rgb(59 130 246 / 0.1) 60deg, transparent 120deg, rgb(99 102 241 / 0.1) 180deg, transparent 240deg, rgb(139 92 246 / 0.1) 300deg, transparent 360deg)`,
          }}></div>
        </div>

        {/* Ligne animée en haut */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-indigo-500 via-purple-500 to-blue-500 bg-[length:200%_100%] animate-pulse"></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-black text-slate-900 mb-6">
              Pourquoi choisir{' '}
              <span className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
                SGDI ?
              </span>
            </h2>
            <p className="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto font-medium leading-relaxed">
              Découvrez les avantages concrets que SGDI apporte à votre organisation
              avec des résultats mesurables et une expérience utilisateur exceptionnelle
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {benefits.map((benefit, index) => (
              <div key={index} className="group relative text-center p-6 rounded-2xl bg-white/70 backdrop-blur-lg border border-slate-200/50 hover:border-blue-300/50 shadow-lg hover:shadow-xl transition-all duration-700 hover:-translate-y-3 overflow-hidden">
                {/* Effet de fond au hover */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-indigo-50/50 to-purple-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

                {/* Effet de brillance */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                <div className="relative z-10">
                  {/* Icône avec effet 3D */}
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-all duration-500 shadow-xl shadow-blue-500/30 group-hover:shadow-blue-500/50">
                      <benefit.icon className="w-8 h-8 text-white group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    {/* Halo lumineux */}
                    <div className="absolute inset-0 w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full blur-xl opacity-0 group-hover:opacity-30 transition-opacity duration-500 mx-auto"></div>
                  </div>

                  {/* Métrique avec animation */}
                  <div className="text-3xl md:text-4xl font-black text-transparent bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text mb-4 group-hover:scale-105 transition-all duration-500">
                    {benefit.metric}
                  </div>

                  <h3 className="text-xl font-bold text-slate-900 mb-4 group-hover:text-blue-700 transition-colors duration-300">
                    {benefit.title}
                  </h3>

                  <p className="text-slate-600 font-medium leading-relaxed text-base">
                    {benefit.description}
                  </p>
                </div>

                {/* Bordure animée */}
                <div className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-gradient-to-r group-hover:from-blue-400 group-hover:to-purple-400 transition-all duration-500"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Section Fonctionnalités */}
      <section id="features" className="py-20 relative overflow-hidden">
        {/* Fond avec motifs complexes */}
        <div className="absolute inset-0 bg-gradient-to-b from-white via-slate-50/50 to-blue-50/30"></div>
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `repeating-linear-gradient(45deg, transparent, transparent 2px, rgb(59 130 246 / 0.1) 2px, rgb(59 130 246 / 0.1) 4px)`,
            backgroundSize: '20px 20px'
          }}></div>
        </div>

        {/* Éléments décoratifs flottants */}
        <div className="absolute top-10 left-10 w-24 h-24 bg-blue-400/5 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-indigo-400/5 rounded-full blur-2xl animate-pulse" style={{animationDelay: '2s'}}></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100/80 to-indigo-100/80 backdrop-blur-sm border border-blue-200/50 rounded-full px-4 py-2 mb-6">
              <Sparkles className="w-4 h-4 text-blue-600" />
              <span className="text-xs font-bold text-slate-700">Fonctionnalités avancées</span>
            </div>

            <h2 className="text-3xl md:text-4xl font-black text-slate-900 mb-6">
              Fonctionnalités{' '}
              <span className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
                principales
              </span>
            </h2>
            <p className="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto font-medium leading-relaxed">
              Découvrez comment SGDI révolutionne la gestion de vos demandes RH avec des outils
              intelligents et une interface moderne conçue pour l'efficacité.
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {features.map((feature, index) => (
              <div key={index} className="group relative bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-slate-200/40 hover:border-blue-300/60 shadow-lg hover:shadow-xl transition-all duration-700 transform hover:-translate-y-3 overflow-hidden">
                {/* Effet de fond animé */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-indigo-50/30 to-purple-50/30 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

                {/* Effet de brillance diagonal */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rotate-12"></div>

                {/* Particules flottantes */}
                <div className="absolute top-3 right-3 w-1.5 h-1.5 bg-blue-400/30 rounded-full animate-ping" style={{animationDelay: `${index * 0.5}s`}}></div>
                <div className="absolute bottom-3 left-3 w-1 h-1 bg-indigo-400/30 rounded-full animate-ping" style={{animationDelay: `${index * 0.7}s`}}></div>

                <div className="relative z-10">
                  {/* Icône avec effet 3D avancé */}
                  <div className="relative mb-6">
                    <div className="w-14 h-14 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-xl shadow-blue-500/25 group-hover:shadow-blue-500/40">
                      <feature.icon className="w-7 h-7 text-white group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    {/* Halo lumineux */}
                    <div className="absolute inset-0 w-14 h-14 bg-gradient-to-br from-blue-400 to-purple-400 rounded-xl blur-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500"></div>
                    {/* Reflet */}
                    <div className="absolute inset-0 w-14 h-14 bg-gradient-to-br from-white/40 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>

                  <h3 className="text-lg font-bold text-slate-900 mb-4 group-hover:text-blue-700 transition-colors duration-300">
                    {feature.title}
                  </h3>

                  <p className="text-slate-600 mb-6 leading-relaxed font-medium text-sm group-hover:text-slate-700 transition-colors duration-300">
                    {feature.description}
                  </p>

                  <ul className="space-y-3">
                    {feature.details.map((detail, idx) => (
                      <li key={idx} className="flex items-center text-slate-600 font-medium text-sm group-hover:text-slate-700 transition-colors duration-300">
                        <div className="relative mr-3">
                          <CheckCircle2 className="w-4 h-4 text-blue-500 group-hover:text-blue-600 group-hover:scale-110 transition-all duration-300" />
                          <div className="absolute inset-0 w-4 h-4 bg-blue-400 rounded-full blur opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
                        </div>
                        <span className="group-hover:translate-x-1 transition-transform duration-300">
                          {detail}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Bordure animée */}
                <div className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-blue-400/30 transition-all duration-500"></div>

                {/* Coin décoratif */}
                <div className="absolute top-0 right-0 w-12 h-12 bg-gradient-to-bl from-blue-500/10 to-transparent rounded-bl-2xl rounded-tr-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Section Comment ça marche */}
      <section className="py-32 relative overflow-hidden">
        {/* Fond avec dégradé complexe et motifs */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/80 via-indigo-50/60 to-purple-50/40"></div>
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `linear-gradient(45deg, rgb(59 130 246 / 0.15) 25%, transparent 25%), linear-gradient(-45deg, rgb(99 102 241 / 0.15) 25%, transparent 25%), linear-gradient(45deg, transparent 75%, rgb(139 92 246 / 0.15) 75%), linear-gradient(-45deg, transparent 75%, rgb(59 130 246 / 0.15) 75%)`,
            backgroundSize: '30px 30px',
            backgroundPosition: '0 0, 0 15px, 15px -15px, -15px 0px'
          }}></div>
        </div>

        {/* Éléments décoratifs animés */}
        <div className="absolute top-20 left-20 w-24 h-24 bg-blue-400/10 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-purple-400/10 rounded-full blur-2xl animate-pulse" style={{animationDelay: '1s'}}></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-24">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100/80 to-purple-100/80 backdrop-blur-sm border border-blue-200/50 rounded-full px-6 py-3 mb-8">
              <Award className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-bold text-slate-700">Processus simplifié</span>
            </div>

            <h2 className="text-4xl md:text-6xl font-black text-slate-900 mb-8">
              Comment ça{' '}
              <span className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
                marche ?
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-slate-600 max-w-4xl mx-auto font-medium leading-relaxed">
              Découvrez en 3 étapes simples comment SGDI transforme votre gestion RH
              et révolutionne votre productivité
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-16 max-w-7xl mx-auto">
            {[
              {
                step: "01",
                title: "Inscription rapide",
                description: "Créez votre compte en moins de 2 minutes et configurez votre espace de travail selon vos besoins spécifiques.",
                icon: Users,
                color: "from-blue-500 to-blue-600",
                bgColor: "from-blue-50 to-blue-100"
              },
              {
                step: "02",
                title: "Configuration simple",
                description: "Paramétrez vos types de demandes, vos workflows personnalisés et invitez votre équipe en quelques clics.",
                icon: Shield,
                color: "from-indigo-500 to-indigo-600",
                bgColor: "from-indigo-50 to-indigo-100"
              },
              {
                step: "03",
                title: "Gestion automatisée",
                description: "Laissez SGDI gérer vos demandes automatiquement avec l'IA et profitez d'un gain de temps considérable.",
                icon: Zap,
                color: "from-purple-500 to-purple-600",
                bgColor: "from-purple-50 to-purple-100"
              }
            ].map((item, index) => (
              <div key={index} className="group relative">
                {/* Ligne de connexion animée */}
                {index < 2 && (
                  <div className="hidden md:block absolute top-20 left-full w-16 z-10 transform translate-x-8">
                    <div className="h-0.5 bg-gradient-to-r from-slate-300 via-blue-400 to-slate-300 relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500 to-transparent w-8 h-full animate-pulse"></div>
                    </div>
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-blue-500 rounded-full animate-pulse shadow-lg shadow-blue-500/50"></div>
                  </div>
                )}

                <div className={`text-center p-10 bg-white/90 backdrop-blur-lg rounded-3xl border border-slate-200/40 shadow-xl hover:shadow-2xl transition-all duration-700 hover:-translate-y-6 group-hover:bg-white overflow-hidden relative`}>
                  {/* Effet de fond au hover */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${item.bgColor} opacity-0 group-hover:opacity-50 transition-opacity duration-700`}></div>

                  {/* Effet de brillance */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                  <div className="relative z-10">
                    {/* Numéro d'étape avec effet 3D */}
                    <div className="relative mb-8">
                      <div className={`w-24 h-24 bg-gradient-to-br ${item.color} rounded-full flex items-center justify-center mx-auto group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 shadow-2xl shadow-blue-500/25 group-hover:shadow-blue-500/40`}>
                        <item.icon className="w-12 h-12 text-white group-hover:scale-110 transition-transform duration-300" />
                      </div>

                      {/* Badge numéro avec animation */}
                      <div className="absolute -top-3 -right-3 w-10 h-10 bg-gradient-to-br from-slate-800 to-slate-900 text-white rounded-full flex items-center justify-center text-lg font-black shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-500">
                        {item.step}
                      </div>

                      {/* Halo lumineux */}
                      <div className={`absolute inset-0 w-24 h-24 bg-gradient-to-br ${item.color} rounded-full blur-xl opacity-0 group-hover:opacity-30 transition-opacity duration-500 mx-auto`}></div>
                    </div>

                    <h3 className="text-2xl font-bold text-slate-900 mb-6 group-hover:text-blue-700 transition-colors duration-300">
                      {item.title}
                    </h3>

                    <p className="text-slate-600 font-medium leading-relaxed text-lg group-hover:text-slate-700 transition-colors duration-300">
                      {item.description}
                    </p>
                  </div>

                  {/* Bordure animée */}
                  <div className="absolute inset-0 rounded-3xl border-2 border-transparent group-hover:border-blue-400/30 transition-all duration-500"></div>
                </div>
              </div>
            ))}
          </div>

          {/* CTA Section améliorée */}
          <div className="text-center mt-20">
            <div className="relative bg-white/80 backdrop-blur-lg rounded-3xl p-12 border border-slate-200/40 shadow-2xl max-w-3xl mx-auto overflow-hidden group hover:shadow-3xl transition-all duration-700">
              {/* Effet de fond animé */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-indigo-50/50 to-purple-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

              {/* Particules flottantes */}
              <div className="absolute top-4 right-4 w-2 h-2 bg-blue-400/30 rounded-full animate-ping"></div>
              <div className="absolute bottom-4 left-4 w-1 h-1 bg-indigo-400/30 rounded-full animate-ping" style={{animationDelay: '1s'}}></div>

              <div className="relative z-10">
                <h3 className="text-3xl md:text-4xl font-black text-slate-900 mb-6 group-hover:text-blue-700 transition-colors duration-300">
                  Prêt à révolutionner votre RH ?
                </h3>
                <p className="text-xl text-slate-600 mb-10 font-medium leading-relaxed">
                  Rejoignez les entreprises qui ont déjà simplifié leur gestion RH
                  et découvrez la puissance de l'automatisation intelligente
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/auth/signin" className="group relative bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white px-10 py-5 rounded-2xl text-xl font-bold hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 transition-all duration-500 transform hover:scale-105 shadow-2xl shadow-blue-500/25 hover:shadow-blue-500/40 inline-flex items-center justify-center overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="flex items-center relative z-10">
                      Démarrer gratuitement
                      <ArrowRight className="w-6 h-6 ml-3 group-hover:translate-x-2 transition-transform duration-300" />
                    </div>
                  </Link>

                  <button className="group flex items-center justify-center border-2 border-slate-300/60 text-slate-700 px-10 py-5 rounded-2xl text-xl font-bold hover:border-blue-400 hover:bg-blue-50/50 transition-all duration-500 bg-white/70 backdrop-blur-sm shadow-lg hover:shadow-xl transform hover:scale-105">
                    <MessageSquare className="w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-300" />
                    Nous contacter
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section Témoignages */}
      <section id="testimonials" className="py-24 bg-gradient-to-br from-slate-100 to-blue-50/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-20">
            <h2 className="text-3xl md:text-5xl font-bold text-slate-900 mb-6">
              Ils nous font confiance
            </h2>
            <p className="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto font-medium">
              Découvrez comment SGDI améliore le quotidien des équipes RH
              dans différentes entreprises.
            </p>
          </div>

          <div className="max-w-5xl mx-auto">
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 md:p-12 shadow-2xl border border-slate-200/60">
              <div className="text-center mb-8">
                <div className="flex justify-center mb-6">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-6 h-6 text-amber-400 fill-current" />
                  ))}
                </div>
                <blockquote className="text-lg md:text-2xl text-slate-700 leading-relaxed mb-8 font-medium italic">
                  "{testimonials[activeTestimonial].content}"
                </blockquote>
                <div className="flex items-center justify-center space-x-4">
                  <div className="w-14 h-14 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                    {testimonials[activeTestimonial].avatar}
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-slate-900 text-lg">{testimonials[activeTestimonial].name}</div>
                    <div className="text-slate-600 font-medium">{testimonials[activeTestimonial].role}</div>
                  </div>
                </div>
              </div>

              <div className="flex justify-center space-x-3">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveTestimonial(index)}
                    className={`h-3 rounded-full transition-all duration-300 ${
                      index === activeTestimonial
                        ? 'bg-blue-600 w-8'
                        : 'bg-slate-300 hover:bg-slate-400 w-3'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section CTA finale */}
      <section className="py-24 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgb(255 255 255 / 0.15) 1px, transparent 0)`,
            backgroundSize: '30px 30px'
          }}></div>
        </div>
        <div className="container mx-auto px-6 text-center relative">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-5xl font-bold text-white mb-6">
              Prêt à simplifier votre gestion RH ?
            </h2>
            <p className="text-lg md:text-xl text-blue-100 mb-12 max-w-2xl mx-auto font-medium">
              Rejoignez les entreprises qui ont déjà amélioré
              leur gestion des demandes avec SGDI.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link href="/auth/signin" className="bg-white text-slate-900 px-8 py-4 rounded-xl text-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-xl inline-flex items-center justify-center">
                Commencer maintenant
              </Link>
              <button className="border-2 border-white/30 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-white/10 hover:border-white/50 transition-all duration-300 backdrop-blur-sm">
                En savoir plus
              </button>
            </div>

            <div className="flex flex-wrap justify-center items-center gap-8 text-blue-100">
              <div className="flex items-center font-medium">
                <CheckCircle2 className="w-5 h-5 mr-2" />
                Gratuit pour commencer
              </div>
              <div className="flex items-center font-medium">
                <CheckCircle2 className="w-5 h-5 mr-2" />
                Support inclus
              </div>
              <div className="flex items-center font-medium">
                <CheckCircle2 className="w-5 h-5 mr-2" />
                Configuration simple
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
        {/* Motif de fond animé */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 2px 2px, rgb(59 130 246 / 0.3) 1px, transparent 0)`,
            backgroundSize: '40px 40px'
          }}></div>
        </div>

        {/* Gradient animé en haut */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 animate-pulse"></div>

        <div className="container mx-auto px-6 py-16 relative">
          {/* Section principale */}
          <div className="grid lg:grid-cols-6 gap-8 mb-12">
            {/* Colonne principale - Logo et description */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6 group">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <span className="text-2xl font-bold text-white">
                  SGDI
                </span>
              </div>
              <p className="text-slate-300 leading-relaxed font-medium mb-6 text-base">
                Système de Gestion des Demandes - La solution moderne
                pour simplifier et automatiser vos processus RH.
              </p>

              {/* Statistiques rapides */}
              <div className="grid grid-cols-2 gap-3 mb-6">
                <div className="bg-slate-800/50 rounded-lg p-3 border border-slate-700/50 hover:border-blue-500/50 transition-colors duration-300">
                  <div className="text-xl font-bold text-blue-400">2K+</div>
                  <div className="text-slate-400 text-xs">Utilisateurs actifs</div>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-3 border border-slate-700/50 hover:border-blue-500/50 transition-colors duration-300">
                  <div className="text-xl font-bold text-blue-400">50+</div>
                  <div className="text-slate-400 text-xs">Entreprises</div>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-3 border border-slate-700/50 hover:border-green-500/50 transition-colors duration-300">
                  <div className="text-xl font-bold text-green-400">99.9%</div>
                  <div className="text-slate-400 text-xs">Disponibilité</div>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-3 border border-slate-700/50 hover:border-purple-500/50 transition-colors duration-300">
                  <div className="text-xl font-bold text-purple-400">24/7</div>
                  <div className="text-slate-400 text-xs">Support</div>
                </div>
              </div>

              {/* Bouton CTA */}
              <Link href="/auth/signin" className="inline-flex items-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg">
                Commencer gratuitement
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>

              {/* Certifications */}
              <div className="mt-6 pt-6 border-t border-slate-700/50">
                <p className="text-slate-400 text-xs mb-3 font-semibold">Certifications & Conformité</p>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 bg-slate-800/30 px-3 py-1 rounded-full">
                    <Lock className="w-3 h-3 text-green-400" />
                    <span className="text-xs text-slate-300 font-medium">RGPD</span>
                  </div>
                  <div className="flex items-center space-x-2 bg-slate-800/30 px-3 py-1 rounded-full">
                    <Shield className="w-3 h-3 text-blue-400" />
                    <span className="text-xs text-slate-300 font-medium">ISO 27001</span>
                  </div>
                  <div className="flex items-center space-x-2 bg-slate-800/30 px-3 py-1 rounded-full">
                    <CheckCircle className="w-3 h-3 text-purple-400" />
                    <span className="text-xs text-slate-300 font-medium">SOC 2</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Liens de navigation */}
            <div>
              <h3 className="font-bold mb-6 text-white text-lg">Produit</h3>
              <ul className="space-y-4 text-slate-300">
                <li><a href="#features" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Fonctionnalités
                </a></li>
                <li><Link href="/auth/signin" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Se connecter
                </Link></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Documentation
                </a></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  API
                </a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold mb-6 text-white text-lg">Entreprise</h3>
              <ul className="space-y-4 text-slate-300">
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  À propos
                </a></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Blog
                </a></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Carrières
                </a></li>
                <li><a href="#contact" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Contact
                </a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold mb-6 text-white text-lg">Support</h3>
              <ul className="space-y-4 text-slate-300">
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Centre d'aide
                </a></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Guides
                </a></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  FAQ
                </a></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Sécurité
                </a></li>
              </ul>
            </div>
          </div>

          {/* Newsletter */}
          <div className="bg-gradient-to-r from-blue-900/30 to-indigo-900/30 rounded-2xl p-8 mb-12 border border-slate-700/50 backdrop-blur-sm">
            <div className="text-center max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-white mb-4">Restez informé</h3>
              <p className="text-slate-300 mb-6 font-medium">
                Recevez les dernières actualités et mises à jour de SGDI directement dans votre boîte mail.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Votre adresse email"
                  className="flex-1 px-4 py-3 rounded-lg bg-slate-800/50 border border-slate-600 text-white placeholder-slate-400 focus:outline-none focus:border-blue-500 transition-colors duration-300"
                />
                <button className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105">
                  S'abonner
                </button>
              </div>
            </div>
          </div>

          {/* Ligne de séparation avec animation */}
          <div className="relative mb-8">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-slate-700"></div>
            </div>
            <div className="relative flex justify-center">
              <div className="bg-slate-900 px-4">
                <div className="w-8 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"></div>
              </div>
            </div>
          </div>

          {/* Footer bottom */}
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <p className="text-slate-400 text-sm font-medium">
                © 2025 SGDI. Tous droits réservés.
              </p>
              <div className="hidden md:flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-slate-400 text-xs">Système opérationnel</span>
              </div>
            </div>

            <div className="flex flex-wrap justify-center md:justify-end items-center gap-6">
              <a href="#" className="text-slate-400 hover:text-blue-400 transition-colors text-sm font-medium">
                Politique de confidentialité
              </a>
              <a href="#" className="text-slate-400 hover:text-blue-400 transition-colors text-sm font-medium">
                Conditions d'utilisation
              </a>
              <a href="#" className="text-slate-400 hover:text-blue-400 transition-colors text-sm font-medium">
                RGPD
              </a>

              {/* Réseaux sociaux */}
              <div className="flex items-center space-x-3 ml-4">
                <a href="#" className="w-8 h-8 bg-slate-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors duration-300 group">
                  <Globe className="w-4 h-4 text-slate-400 group-hover:text-white" />
                </a>
                <a href="#" className="w-8 h-8 bg-slate-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors duration-300 group">
                  <MessageSquare className="w-4 h-4 text-slate-400 group-hover:text-white" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}